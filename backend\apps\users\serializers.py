"""
用户管理模块 - 序列化器
"""
from rest_framework import serializers
from django.contrib.auth.hashers import make_password
from django.contrib.auth.password_validation import validate_password
from django.core.exceptions import ValidationError
from apps.common.exceptions import BusinessException, ErrorCode
from .models import UserProfile


class UserSerializer(serializers.ModelSerializer):
    """用户序列化器 - 用于列表和详情展示"""
    
    # 只读字段
    id = serializers.IntegerField(read_only=True)
    created_at = serializers.DateTimeField(read_only=True, format='%Y-%m-%d %H:%M:%S')
    updated_at = serializers.DateTimeField(read_only=True, format='%Y-%m-%d %H:%M:%S')
    last_login_time = serializers.DateTimeField(read_only=True, format='%Y-%m-%d %H:%M:%S')
    last_login_ip = serializers.IPAddressField(read_only=True)
    login_fail_count = serializers.IntegerField(read_only=True)
    locked_until = serializers.DateTimeField(read_only=True, format='%Y-%m-%d %H:%M:%S')
    
    # 密码字段不返回
    password = serializers.CharField(write_only=True, required=False)
    
    class Meta:
        model = UserProfile
        fields = [
            'id', 'username', 'nickname', 'email', 'phone', 'wechat_work_id',
            'avatar', 'is_active', 'is_staff', 'is_superuser',
            'created_at', 'updated_at', 'last_login_time', 'last_login_ip',
            'login_fail_count', 'locked_until', 'password'
        ]
        extra_kwargs = {
            'username': {'required': True},
            'nickname': {'required': True},
            'email': {'required': False},
            'phone': {'required': False},
            'wechat_work_id': {'required': False},
            'avatar': {'required': False},
            'is_active': {'default': True},
            'is_staff': {'default': False},
            'is_superuser': {'default': False},
        }
    
    def validate_username(self, value):
        """验证用户名唯一性"""
        # 更新时排除当前用户
        queryset = UserProfile.objects.filter(username=value)
        if self.instance:
            queryset = queryset.exclude(pk=self.instance.pk)
        
        if queryset.exists():
            raise serializers.ValidationError("用户名已存在")
        return value
    
    def validate_email(self, value):
        """验证邮箱唯一性（如果提供）"""
        if value:
            queryset = UserProfile.objects.filter(email=value)
            if self.instance:
                queryset = queryset.exclude(pk=self.instance.pk)
            
            if queryset.exists():
                raise serializers.ValidationError("邮箱已存在")
        return value
    
    def validate_phone(self, value):
        """验证手机号唯一性（如果提供）"""
        if value:
            queryset = UserProfile.objects.filter(phone=value)
            if self.instance:
                queryset = queryset.exclude(pk=self.instance.pk)
            
            if queryset.exists():
                raise serializers.ValidationError("手机号已存在")
        return value
    
    def update(self, instance, validated_data):
        """更新用户信息"""
        # 如果包含密码，需要加密
        password = validated_data.pop('password', None)
        if password:
            validated_data['password'] = make_password(password)
        
        return super().update(instance, validated_data)


class UserCreateSerializer(serializers.ModelSerializer):
    """用户创建序列化器"""
    
    password = serializers.CharField(write_only=True, min_length=8, max_length=128)
    password_confirm = serializers.CharField(write_only=True, min_length=8, max_length=128)
    
    class Meta:
        model = UserProfile
        fields = [
            'username', 'nickname', 'email', 'phone', 'wechat_work_id',
            'avatar', 'is_active', 'is_staff', 'is_superuser',
            'password', 'password_confirm'
        ]
        extra_kwargs = {
            'username': {'required': True},
            'nickname': {'required': True},
            'email': {'required': False},
            'phone': {'required': False},
            'wechat_work_id': {'required': False},
            'avatar': {'required': False},
            'is_active': {'default': True},
            'is_staff': {'default': False},
            'is_superuser': {'default': False},
        }
    
    def validate_username(self, value):
        """验证用户名唯一性"""
        if UserProfile.objects.filter(username=value).exists():
            raise serializers.ValidationError("用户名已存在")
        return value
    
    def validate_email(self, value):
        """验证邮箱唯一性（如果提供）"""
        if value and UserProfile.objects.filter(email=value).exists():
            raise serializers.ValidationError("邮箱已存在")
        return value
    
    def validate_phone(self, value):
        """验证手机号唯一性（如果提供）"""
        if value and UserProfile.objects.filter(phone=value).exists():
            raise serializers.ValidationError("手机号已存在")
        return value
    
    def validate_password(self, value):
        """验证密码强度"""
        try:
            validate_password(value)
        except ValidationError as e:
            raise serializers.ValidationError(list(e.messages))
        return value
    
    def validate(self, attrs):
        """验证密码确认"""
        password = attrs.get('password')
        password_confirm = attrs.pop('password_confirm', None)
        
        if password != password_confirm:
            raise serializers.ValidationError({
                'password_confirm': '两次输入的密码不一致'
            })
        
        return attrs
    
    def create(self, validated_data):
        """创建用户"""
        # 移除确认密码字段
        validated_data.pop('password_confirm', None)
        
        # 加密密码
        password = validated_data.pop('password')
        user = UserProfile.objects.create(**validated_data)
        user.set_password(password)
        user.save()
        
        return user


class UserProfileSerializer(serializers.ModelSerializer):
    """当前用户个人资料序列化器"""
    
    class Meta:
        model = UserProfile
        fields = [
            'id', 'username', 'nickname', 'email', 'phone', 'wechat_work_id',
            'avatar', 'last_login_time', 'last_login_ip', 'created_at'
        ]
        read_only_fields = [
            'id', 'username', 'last_login_time', 'last_login_ip', 'created_at'
        ]
    
    def validate_email(self, value):
        """验证邮箱唯一性（如果提供）"""
        if value:
            queryset = UserProfile.objects.filter(email=value)
            if self.instance:
                queryset = queryset.exclude(pk=self.instance.pk)
            
            if queryset.exists():
                raise serializers.ValidationError("邮箱已存在")
        return value
    
    def validate_phone(self, value):
        """验证手机号唯一性（如果提供）"""
        if value:
            queryset = UserProfile.objects.filter(phone=value)
            if self.instance:
                queryset = queryset.exclude(pk=self.instance.pk)
            
            if queryset.exists():
                raise serializers.ValidationError("手机号已存在")
        return value


class PasswordResetSerializer(serializers.Serializer):
    """密码重置序列化器"""
    
    new_password = serializers.CharField(write_only=True, min_length=8, max_length=128)
    new_password_confirm = serializers.CharField(write_only=True, min_length=8, max_length=128)
    
    def validate_new_password(self, value):
        """验证新密码强度"""
        try:
            validate_password(value)
        except ValidationError as e:
            raise serializers.ValidationError(list(e.messages))
        return value
    
    def validate(self, attrs):
        """验证密码确认"""
        new_password = attrs.get('new_password')
        new_password_confirm = attrs.get('new_password_confirm')
        
        if new_password != new_password_confirm:
            raise serializers.ValidationError({
                'new_password_confirm': '两次输入的密码不一致'
            })
        
        return attrs


class ChangePasswordSerializer(serializers.Serializer):
    """修改密码序列化器（当前用户）"""
    
    old_password = serializers.CharField(write_only=True)
    new_password = serializers.CharField(write_only=True, min_length=8, max_length=128)
    new_password_confirm = serializers.CharField(write_only=True, min_length=8, max_length=128)
    
    def validate_new_password(self, value):
        """验证新密码强度"""
        try:
            validate_password(value)
        except ValidationError as e:
            raise serializers.ValidationError(list(e.messages))
        return value
    
    def validate(self, attrs):
        """验证密码确认"""
        new_password = attrs.get('new_password')
        new_password_confirm = attrs.get('new_password_confirm')
        
        if new_password != new_password_confirm:
            raise serializers.ValidationError({
                'new_password_confirm': '两次输入的密码不一致'
            })
        
        return attrs
    
    def validate_old_password(self, value):
        """验证原密码"""
        user = self.context['request'].user
        if not user.check_password(value):
            raise serializers.ValidationError("原密码错误")
        return value


class UserListSerializer(serializers.ModelSerializer):
    """用户列表序列化器 - 简化字段"""
    
    class Meta:
        model = UserProfile
        fields = [
            'id', 'username', 'nickname', 'email', 'phone',
            'is_active', 'is_staff', 'created_at', 'last_login_time'
        ]
        read_only_fields = fields
