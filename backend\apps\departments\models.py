"""
部门管理模块 - 部门模型
"""
from django.db import models
from django.utils import timezone
from mptt.models import MPTTModel, TreeForeignKey
from apps.common.models import BaseModel, ActiveManager


class Department(MPTTModel, BaseModel):
    """部门模型 - 支持多主管"""
    name = models.CharField(max_length=100, verbose_name="部门名称")
    code = models.CharField(max_length=50, unique=True, verbose_name="部门编码")
    parent = TreeForeignKey('self', on_delete=models.CASCADE, null=True, blank=True, 
                           related_name='children', verbose_name="上级部门")
    
    # 部门信息
    description = models.TextField(blank=True, verbose_name="部门描述")
    sort_order = models.IntegerField(default=0, verbose_name="排序")
    is_active = models.BooleanField(default=True, verbose_name="是否激活")
    
    # 管理器
    objects = ActiveManager()
    all_objects = models.Manager()
    
    class MPTTMeta:
        order_insertion_by = ['sort_order', 'name']
    
    class Meta:
        db_table = 'sys_department'
        verbose_name = "部门"
        verbose_name_plural = "部门"
        indexes = [
            models.Index(fields=['parent', 'is_active']),
            models.Index(fields=['code']),
        ]
    
    def get_managers(self, level=None):
        """获取部门主管"""
        queryset = self.userdepartment_set.filter(
            is_manager=True,
            is_deleted=False
        ).select_related('user')
        if level:
            queryset = queryset.filter(manager_level=level)
        return queryset.order_by('manager_level', 'weight')
    
    def get_primary_manager(self):
        """获取主要主管（一级主管中权重最高的）"""
        return self.get_managers(level=1).first()
    
    def __str__(self):
        return self.name


class UserDepartment(BaseModel):
    """用户部门关联 - 支持多部门兼职和多主管"""
    user = models.ForeignKey('users.UserProfile', on_delete=models.CASCADE, verbose_name="用户")
    department = models.ForeignKey(Department, on_delete=models.CASCADE, verbose_name="部门")
    
    # 关联属性
    is_primary = models.BooleanField(default=False, verbose_name="是否主部门")
    is_manager = models.BooleanField(default=False, verbose_name="是否部门主管")
    position = models.CharField(max_length=100, blank=True, verbose_name="职位")
    
    # 主管级别（支持多级主管）
    MANAGER_LEVEL_CHOICES = [
        (1, '一级主管'),
        (2, '二级主管'),
        (3, '三级主管'),
    ]
    manager_level = models.IntegerField(
        choices=MANAGER_LEVEL_CHOICES, 
        null=True, 
        blank=True, 
        verbose_name="主管级别"
    )
    
    # 权重（用于权限计算时的优先级）
    weight = models.IntegerField(default=1, verbose_name="权重")
    
    # 生效时间
    effective_date = models.DateField(default=timezone.now, verbose_name="生效日期")
    expiry_date = models.DateField(null=True, blank=True, verbose_name="失效日期")
    
    # 管理器
    objects = ActiveManager()
    all_objects = models.Manager()
    
    class Meta:
        db_table = 'sys_user_department'
        unique_together = ['user', 'department']
        verbose_name = "用户部门关联"
        verbose_name_plural = "用户部门关联"
        
        # 数据库约束：确保每个用户只有一个主部门
        constraints = [
            models.UniqueConstraint(
                fields=['user'], 
                condition=models.Q(is_primary=True, is_deleted=False),
                name='unique_primary_department_per_user'
            )
        ]
        
        indexes = [
            models.Index(fields=['user', 'is_manager']),
            models.Index(fields=['department', 'is_manager']),
            models.Index(fields=['effective_date', 'expiry_date']),
        ]
    
    def is_effective(self):
        """检查关联是否在有效期内"""
        now = timezone.now().date()
        if self.effective_date > now:
            return False
        if self.expiry_date and self.expiry_date < now:
            return False
        return True
    
    @classmethod
    def get_effective_relations(cls, user=None, department=None):
        """获取有效的用户部门关联"""
        now = timezone.now().date()
        queryset = cls.objects.filter(
            effective_date__lte=now,
            is_deleted=False
        ).filter(
            models.Q(expiry_date__isnull=True) | models.Q(expiry_date__gte=now)
        )
        
        if user:
            queryset = queryset.filter(user=user)
        if department:
            queryset = queryset.filter(department=department)
            
        return queryset
    
    def __str__(self):
        return f"{self.user.nickname} - {self.department.name}"