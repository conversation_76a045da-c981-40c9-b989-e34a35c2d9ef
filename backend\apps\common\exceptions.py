# -*- coding: utf-8 -*-
"""
异常处理模块
"""
import logging
from datetime import datetime
from rest_framework.views import exception_handler
from rest_framework.response import Response
from rest_framework import status
from django.core.exceptions import ValidationError
from django.db import IntegrityError
from .response import ApiResponse

logger = logging.getLogger(__name__)


class ErrorCode:
    """错误码定义常量类"""
    
    # 成功状态码
    SUCCESS = 1000
    
    # 认证错误 (2000-2099)
    LOGIN_FAILED = 2001
    CAPTCHA_ERROR = 2002
    TOKEN_EXPIRED = 2003
    TOKEN_INVALID = 2004
    ACCOUNT_LOCKED = 2005
    ACCOUNT_DISABLED = 2006
    PASSWORD_INCORRECT = 2007
    USER_NOT_AUTHENTICATED = 2008
    
    # 权限错误 (2100-2199)
    PERMISSION_DENIED = 2101
    INSUFFICIENT_PERMISSIONS = 2102
    ACCESS_FORBIDDEN = 2103
    ROLE_PERMISSION_DENIED = 2104
    DATA_SCOPE_DENIED = 2105
    
    # 业务错误 (3000-3999)
    USER_NOT_FOUND = 3001
    USER_ALREADY_EXISTS = 3002
    DEPARTMENT_HAS_CHILDREN = 3003
    ROLE_IN_USE = 3004
    INVALID_PARAMETER = 3005
    RESOURCE_NOT_FOUND = 3006
    DUPLICATE_RESOURCE = 3007
    OPERATION_NOT_ALLOWED = 3008
    DATA_INTEGRITY_ERROR = 3009
    VALIDATION_ERROR = 3010

    # 用户管理相关错误 (3100-3199)
    USER_CREATE_FAILED = 3101
    USER_UPDATE_FAILED = 3102
    USER_DELETE_FAILED = 3103
    USER_RESTORE_FAILED = 3104
    USER_NOT_DELETED = 3105
    CANNOT_DELETE_SELF = 3106
    CANNOT_MODIFY_SELF = 3107
    PROFILE_UPDATE_FAILED = 3108
    PASSWORD_RESET_FAILED = 3109
    PASSWORD_CHANGE_FAILED = 3110
    USER_STATUS_CHANGE_FAILED = 3111
    
    # 系统错误 (5000-5999)
    INTERNAL_ERROR = 5000
    DATABASE_ERROR = 5001
    NETWORK_ERROR = 5002
    SERVICE_UNAVAILABLE = 5003
    TIMEOUT_ERROR = 5004
    CONFIGURATION_ERROR = 5005


class BusinessException(Exception):
    """
    自定义业务异常类
    
    用于处理业务逻辑中的异常情况，提供统一的异常处理机制
    """
    
    def __init__(self, code=ErrorCode.INTERNAL_ERROR, message="业务异常", data=None):
        """
        初始化业务异常
        
        Args:
            code: 错误码
            message: 错误消息
            data: 附加数据
        """
        self.code = code
        self.message = message
        self.data = data
        super().__init__(message)
    
    def __str__(self):
        return f"BusinessException(code={self.code}, message={self.message})"


def custom_exception_handler(exc, context):
    """
    自定义全局异常处理器
    
    处理所有API请求中的异常，返回统一格式的错误响应
    
    Args:
        exc: 异常实例
        context: 异常上下文
        
    Returns:
        Response: 统一格式的错误响应
    """
    # 获取请求信息用于日志记录
    request = context.get('request')
    view = context.get('view')
    
    # 记录异常信息
    if request:
        logger.error(
            f"API异常 - 路径: {request.path}, 方法: {request.method}, "
            f"用户: {getattr(request.user, 'username', 'Anonymous')}, "
            f"异常: {exc.__class__.__name__}: {str(exc)}",
            exc_info=True
        )
    
    # 处理自定义业务异常
    if isinstance(exc, BusinessException):
        return ApiResponse.error(
            message=exc.message,
            code=exc.code,
            data=exc.data,
            status_code=status.HTTP_400_BAD_REQUEST
        )
    
    # 处理Django验证异常
    if isinstance(exc, ValidationError):
        if hasattr(exc, 'message_dict'):
            # 字段验证错误
            error_messages = []
            for field, messages in exc.message_dict.items():
                for message in messages:
                    error_messages.append(f"{field}: {message}")
            message = "; ".join(error_messages)
        else:
            # 非字段验证错误
            message = "; ".join(exc.messages) if hasattr(exc, 'messages') else str(exc)
        
        return ApiResponse.error(
            message=message,
            code=ErrorCode.VALIDATION_ERROR,
            status_code=status.HTTP_400_BAD_REQUEST
        )
    
    # 处理数据库完整性错误
    if isinstance(exc, IntegrityError):
        message = "数据完整性错误，可能存在重复数据或违反约束条件"
        return ApiResponse.error(
            message=message,
            code=ErrorCode.DATA_INTEGRITY_ERROR,
            status_code=status.HTTP_400_BAD_REQUEST
        )
    
    # 调用DRF默认异常处理器
    response = exception_handler(exc, context)
    
    if response is not None:
        # 处理DRF标准异常
        custom_response_data = {
            'code': _get_error_code_from_status(response.status_code),
            'message': _get_error_message_from_response(response),
            'data': None,
            'timestamp': datetime.now().isoformat()
        }
        
        response.data = custom_response_data
        return response
    
    # 处理未捕获的系统异常
    logger.error(f"未处理的系统异常: {exc}", exc_info=True)
    return ApiResponse.error(
        message="系统内部错误，请稍后重试",
        code=ErrorCode.INTERNAL_ERROR,
        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR
    )


def _get_error_code_from_status(status_code):
    """根据HTTP状态码获取对应的业务错误码"""
    status_code_mapping = {
        400: ErrorCode.INVALID_PARAMETER,
        401: ErrorCode.USER_NOT_AUTHENTICATED,
        403: ErrorCode.PERMISSION_DENIED,
        404: ErrorCode.RESOURCE_NOT_FOUND,
        405: ErrorCode.OPERATION_NOT_ALLOWED,
        500: ErrorCode.INTERNAL_ERROR,
        502: ErrorCode.SERVICE_UNAVAILABLE,
        503: ErrorCode.SERVICE_UNAVAILABLE,
        504: ErrorCode.TIMEOUT_ERROR,
    }
    return status_code_mapping.get(status_code, ErrorCode.INTERNAL_ERROR)


def _get_error_message_from_response(response):
    """从DRF响应中提取错误消息"""
    if isinstance(response.data, dict):
        # 处理字段验证错误
        if 'detail' in response.data:
            return response.data['detail']
        
        # 处理多字段错误
        error_messages = []
        for field, messages in response.data.items():
            if isinstance(messages, list):
                for message in messages:
                    error_messages.append(f"{field}: {message}")
            else:
                error_messages.append(f"{field}: {messages}")
        
        if error_messages:
            return "; ".join(error_messages)
    
    elif isinstance(response.data, list):
        return "; ".join(str(item) for item in response.data)
    
    return str(response.data) if response.data else "请求处理失败"